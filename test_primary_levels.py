from app import app, db, add_subjects_to_level
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def test_create_primary_levels():
    """اختبار إنشاء المستويات الخمسة للتعليم الابتدائي"""
    with app.app_context():
        # المستويات الخمسة للتعليم الابتدائي
        primary_levels = [
            'السنة الأولى ابتدائي',
            'السنة الثانية ابتدائي', 
            'السنة الثالثة ابتدائي',
            'السنة الرابعة ابتدائي',
            'السنة الخامسة ابتدائي'
        ]
        
        created_levels = 0
        created_databases = 0
        
        for level_name in primary_levels:
            print(f"معالجة المستوى: {level_name}")
            
            # البحث عن المستوى أو إنشاؤه
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if not level:
                level = EducationalLevel(
                    name=level_name,
                    is_active=True,
                    database_prefix=f'primary_{level_name.split()[1]}'  # primary_الأولى، primary_الثانية، إلخ
                )
                db.session.add(level)
                db.session.flush()
                created_levels += 1
                print(f"  تم إنشاء المستوى: {level_name}")
            else:
                print(f"  المستوى موجود مسبقاً: {level_name}")

            # البحث عن قاعدة البيانات أو إنشاؤها
            level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
            if not level_db:
                level_db = LevelDatabase(
                    level_id=level.id,
                    name=f'قاعدة بيانات {level_name}',
                    file_path=f'data/{level.database_prefix}_education.db',
                    is_active=True
                )
                db.session.add(level_db)
                db.session.flush()
                created_databases += 1
                print(f"  تم إنشاء قاعدة البيانات: {level_db.name}")
            else:
                # حذف جميع البيانات الموجودة في قاعدة البيانات
                deleted_count = LevelDataEntry.query.filter_by(database_id=level_db.id).count()
                LevelDataEntry.query.filter_by(database_id=level_db.id).delete()
                print(f"  تم حذف {deleted_count} عنصر من قاعدة البيانات الموجودة")

            # إضافة المواد والميادين لهذا المستوى
            print(f"  إضافة المواد والميادين...")
            add_subjects_to_level(level_db.id)
            
            # عد المواد والميادين المضافة
            subjects_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
            domains_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
            print(f"  تم إضافة {subjects_count} مادة و {domains_count} ميدان")

        db.session.commit()
        print(f"\n✅ تم الانتهاء بنجاح!")
        print(f"📊 الإحصائيات:")
        print(f"   - تم إنشاء {created_levels} مستوى تعليمي جديد")
        print(f"   - تم إنشاء {created_databases} قاعدة بيانات جديدة")
        
        # عرض ملخص النتائج
        print(f"\n📋 ملخص المستويات:")
        for level_name in primary_levels:
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if level:
                level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
                if level_db:
                    subjects_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
                    domains_count = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
                    print(f"   - {level_name}: {subjects_count} مادة، {domains_count} ميدان")

if __name__ == "__main__":
    test_create_primary_levels()
