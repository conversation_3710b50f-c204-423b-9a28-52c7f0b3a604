{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">إدارة جدول التدريس</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-calendar-alt me-1"></i>
                جدول التدريس الأسبوعي
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>اليوم</th>
                                <th>الوقت</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set days = ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'] %}
                            {% for schedule in schedules %}
                            <tr>
                                <td>{{ days[schedule.day_of_week] }}</td>
                                <td>{{ schedule.start_time.strftime('%H:%M') }} - {{ schedule.end_time.strftime('%H:%M') }}</td>
                                <td>{{ schedule.level.name }}</td>
                                <td>{{ schedule.subject.name }}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-schedule" data-id="{{ schedule.id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-schedule" data-id="{{ schedule.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-plus me-1"></i>
                إضافة حصة جديدة
            </div>
            <div class="card-body">
                <form id="scheduleForm" method="POST" action="{{ url_for('add_schedule') }}">
                    <div class="mb-3">
                        <label for="day_of_week" class="form-label">اليوم</label>
                        <select class="form-select" id="day_of_week" name="day_of_week" required>
                            <option value="" selected disabled>اختر اليوم</option>
                            <option value="0">الإثنين</option>
                            <option value="1">الثلاثاء</option>
                            <option value="2">الأربعاء</option>
                            <option value="3">الخميس</option>
                            <option value="4">الجمعة</option>
                            <option value="5">السبت</option>
                            <option value="6">الأحد</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="start_time" class="form-label">وقت البداية</label>
                        <input type="time" class="form-control" id="start_time" name="start_time" required>
                    </div>
                    <div class="mb-3">
                        <label for="end_time" class="form-label">وقت النهاية</label>
                        <input type="time" class="form-control" id="end_time" name="end_time" required>
                    </div>
                    <div class="mb-3">
                        <label for="level_id" class="form-label">المستوى التعليمي</label>
                        <select class="form-select" id="level_id" name="level_id" required>
                            <option value="" selected disabled>اختر المستوى</option>
                            {% for level in levels %}
                            <option value="{{ level.id }}">{{ level.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="subject_id" class="form-label">المادة</label>
                        <select class="form-select" id="subject_id" name="subject_id" disabled required>
                            <option value="" selected disabled>اختر المادة</option>
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ الحصة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الحصة؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Schedule Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">تعديل الحصة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" method="POST">
                    <div class="mb-3">
                        <label for="edit_day_of_week" class="form-label">اليوم</label>
                        <select class="form-select" id="edit_day_of_week" name="day_of_week" required>
                            <option value="" disabled>اختر اليوم</option>
                            <option value="0">الإثنين</option>
                            <option value="1">الثلاثاء</option>
                            <option value="2">الأربعاء</option>
                            <option value="3">الخميس</option>
                            <option value="4">الجمعة</option>
                            <option value="5">السبت</option>
                            <option value="6">الأحد</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_start_time" class="form-label">وقت البداية</label>
                        <input type="time" class="form-control" id="edit_start_time" name="start_time" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_end_time" class="form-label">وقت النهاية</label>
                        <input type="time" class="form-control" id="edit_end_time" name="end_time" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_level_id" class="form-label">المستوى التعليمي</label>
                        <select class="form-select" id="edit_level_id" name="level_id" required>
                            <option value="" disabled>اختر المستوى</option>
                            {% for level in levels %}
                            <option value="{{ level.id }}">المستوى {{ level.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_subject_id" class="form-label">المادة</label>
                        <select class="form-select" id="edit_subject_id" name="subject_id" required>
                            <option value="" disabled>اختر المادة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Level change event
        const levelSelect = document.getElementById('level_id');
        const subjectSelect = document.getElementById('subject_id');

        levelSelect.addEventListener('change', function() {
            // Reset subject dropdown
            subjectSelect.innerHTML = '<option value="" selected disabled>اختر المادة</option>';
            subjectSelect.disabled = false;

            // Fetch subjects for selected level
            fetch(`/api/subjects/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate subject dropdown
                    data.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.id;
                        option.textContent = subject.name;

                        // إضافة سمة لتحديد مصدر المادة
                        option.setAttribute('data-source', subject.source || '');

                        // إضافة علامة للمواد من قاعدة البيانات
                        if (subject.source === 'database') {
                            option.textContent = `${subject.name} (من قاعدة البيانات)`;
                        }

                        subjectSelect.appendChild(option);
                    });
                });
        });

        // Delete schedule
        const deleteButtons = document.querySelectorAll('.delete-schedule');
        const deleteForm = document.getElementById('deleteForm');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const scheduleId = this.getAttribute('data-id');
                deleteForm.action = `/schedule/delete/${scheduleId}`;
                deleteModal.show();
            });
        });

        // Edit schedule
        const editButtons = document.querySelectorAll('.edit-schedule');
        const editModal = new bootstrap.Modal(document.getElementById('editModal'));
        const editForm = document.getElementById('editForm');
        const saveEditBtn = document.getElementById('saveEditBtn');
        const editLevelSelect = document.getElementById('edit_level_id');
        const editSubjectSelect = document.getElementById('edit_subject_id');

        // دالة لتحميل المواد الدراسية للمستوى المحدد
        function loadSubjectsForLevel(levelId, selectedSubjectId = null) {
            // إعادة تعيين قائمة المواد
            editSubjectSelect.innerHTML = '<option value="" disabled selected>اختر المادة</option>';
            editSubjectSelect.disabled = false;

            // جلب المواد للمستوى المحدد
            fetch(`/api/subjects/${levelId}`)
                .then(response => response.json())
                .then(data => {
                    // ملء قائمة المواد
                    data.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.id;
                        option.textContent = subject.name;

                        // إضافة سمة لتحديد مصدر المادة
                        option.setAttribute('data-source', subject.source || '');

                        // إضافة علامة للمواد من قاعدة البيانات
                        if (subject.source === 'database') {
                            option.textContent = `${subject.name} (من قاعدة البيانات)`;
                        }

                        editSubjectSelect.appendChild(option);

                        // تحديد المادة المختارة إذا كانت محددة
                        if (selectedSubjectId && subject.id == selectedSubjectId) {
                            option.selected = true;
                        }
                    });
                });
        }

        // تغيير المستوى في نموذج التعديل
        editLevelSelect.addEventListener('change', function() {
            loadSubjectsForLevel(this.value);
        });

        // معالجة نقر زر التعديل
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const scheduleId = this.getAttribute('data-id');

                // جلب تفاصيل الحصة
                fetch(`/api/schedule/${scheduleId}`)
                    .then(response => response.json())
                    .then(schedule => {
                        // ملء النموذج ببيانات الحصة
                        document.getElementById('edit_day_of_week').value = schedule.day_of_week;
                        document.getElementById('edit_start_time').value = schedule.start_time;
                        document.getElementById('edit_end_time').value = schedule.end_time;
                        document.getElementById('edit_level_id').value = schedule.level_id;

                        // تحميل المواد للمستوى المحدد وتحديد المادة الحالية
                        loadSubjectsForLevel(schedule.level_id, schedule.subject_id);

                        // تعيين معرف الحصة لاستخدامه عند الحفظ
                        editForm.setAttribute('data-schedule-id', scheduleId);

                        // عرض النافذة
                        editModal.show();
                    })
                    .catch(error => {
                        console.error('Error fetching schedule details:', error);
                        alert('حدث خطأ أثناء جلب تفاصيل الحصة');
                    });
            });
        });

        // معالجة نقر زر الحفظ
        saveEditBtn.addEventListener('click', function() {
            const scheduleId = editForm.getAttribute('data-schedule-id');
            const formData = new FormData(editForm);

            // إرسال البيانات لتحديث الحصة
            fetch(`/schedule/edit/${scheduleId}`, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    // إغلاق النافذة وإعادة تحميل الصفحة
                    editModal.hide();
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'حدث خطأ أثناء تحديث الحصة');
                    });
                }
            })
            .catch(error => {
                console.error('Error updating schedule:', error);
                alert(error.message || 'حدث خطأ أثناء تحديث الحصة');
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
