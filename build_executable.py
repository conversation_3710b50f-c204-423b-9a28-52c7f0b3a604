"""
سكريبت لإنشاء ملف تنفيذي لتطبيق Ta9affi باستخدام PyInstaller
"""

import os
import subprocess
import shutil
import sys

def build_executable():
    print("جاري إنشاء ملف تنفيذي لتطبيق Ta9affi v1.04...")

    # التحقق من تثبيت PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("جاري تثبيت PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)

    # إنشاء دليل البناء إذا لم يكن موجودًا
    if not os.path.exists("build"):
        os.makedirs("build")

    # تنظيف دليل dist إذا كان موجودًا
    if os.path.exists("dist"):
        shutil.rmtree("dist")

    # إنشاء ملف spec لـ PyInstaller
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ta9affi_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('data', 'data'),
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'werkzeug',
        'pandas',
        'openpyxl',
        'jinja2',
        'sqlalchemy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Ta9affi',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/img/favicon.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Ta9affi',
)
"""

    with open("Ta9affi.spec", "w") as f:
        f.write(spec_content)

    # تشغيل PyInstaller
    print("جاري تشغيل PyInstaller...")
    subprocess.run(["pyinstaller", "Ta9affi.spec", "--clean"], check=True)

    print("تم إنشاء الملف التنفيذي بنجاح في مجلد 'dist/Ta9affi'")

if __name__ == "__main__":
    build_executable()
