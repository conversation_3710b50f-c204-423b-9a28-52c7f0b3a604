# Ta9affi - نظام إدارة البرنامج السنوي للتدريس

نظام إدارة البرنامج السنوي للتدريس في التعليم الإبتدائي في الجزائر.

## الوصف

تطبيق ويب يسمح للإدارة العامة والمفتشين والأساتذة بمتابعة تقدم البرنامج السنوي للتدريس. يمكن للإدارة العامة أن تطلع على المفتشين، والمفتشين يمكنهم الإطلاع على مدى تقدم الأساتذة تحت إشرافهم في البرنامج السنوي لجميع المستويات في التعليم الإبتدائي.

## المميزات

- نظام تسجيل دخول متعدد المستويات (إدارة، مفتش، أستاذ)
- إدارة البرنامج السنوي للتدريس
- تتبع تقدم الأساتذة في تنفيذ البرنامج
- إدارة جدول التدريس الخاص بكل أستاذ
- نظام قوائم منسدلة متسلسلة (المستوى، المادة، الميدان، المواد المعرفية، الكفاءة المستهدفة)
- استيراد وتصدير البيانات بصيغة Excel
- واجهة مستخدم عصرية ومتجاوبة باستخدام Bootstrap

## متطلبات النظام

- Python 3.8+
- Flask
- SQLAlchemy
- Flask-Login
- Flask-WTF
- pandas
- openpyxl

## التثبيت

1. قم بتثبيت Python 3.8 أو أحدث
2. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   ```
3. قم بتفعيل البيئة الافتراضية:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`
4. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```

## تشغيل التطبيق

1. قم بتفعيل البيئة الافتراضية إذا لم تكن مفعلة بالفعل
2. قم بإضافة البيانات التجريبية (اختياري):
   ```
   python seed_data.py
   ```
3. قم بتشغيل التطبيق:
   ```
   python app.py
   ```
4. افتح المتصفح وانتقل إلى `http://localhost:5000`

## حسابات المستخدمين التجريبية

يمكنك استخدام الحسابات التالية للدخول إلى النظام بعد تشغيل ملف `seed_data.py`:

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`

ملاحظة: تم ربط المفتش بالأستاذين في النظام، بحيث يمكن للمفتش متابعة تقدمهم في البرنامج السنوي.

## هيكل المشروع

```
Ta9affi/
├── app.py                  # نقطة الدخول الرئيسية للتطبيق
├── models.py               # نماذج قاعدة البيانات
├── routes.py               # مسارات التطبيق
├── seed_data.py            # ملف إضافة البيانات التجريبية
├── requirements.txt        # متطلبات التطبيق
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── img/                # الصور
└── templates/              # قوالب HTML
    ├── base.html           # القالب الأساسي
    ├── index.html          # الصفحة الرئيسية
    ├── login.html          # صفحة تسجيل الدخول
    ├── register.html       # صفحة إنشاء حساب
    ├── admin_dashboard.html # لوحة تحكم الإدارة
    ├── inspector_dashboard.html # لوحة تحكم المفتش
    ├── teacher_dashboard.html # لوحة تحكم الأستاذ
    ├── teaching_program.html # صفحة البرنامج السنوي
    └── manage_schedule.html # صفحة إدارة جدول التدريس
```

## المساهمة

نرحب بمساهماتكم! يرجى إرسال طلبات السحب أو فتح مشكلة لمناقشة التغييرات المقترحة.

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT.
