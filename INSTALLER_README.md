# إنشاء مثبت Ta9affi v1.04

يشرح هذا المستند كيفية إنشاء مثبت لنظام ويندوز 7 والإصدارات الأحدث لتطبيق Ta9affi.

## المتطلبات المسبقة

1. **بايثون 3.8 أو أحدث**
   - التحميل: https://www.python.org/downloads/
   - تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت

2. **NSIS (Nullsoft Scriptable Install System)**
   - التحميل: https://nsis.sourceforge.io/Download
   - قم بتثبيت النسخة الكاملة

## الملفات المضمنة

- `ta9affi_launcher.py`: نقطة الدخول للتطبيق المحزم
- `build_executable.py`: سكريبت لإنشاء الملف التنفيذي باستخدام PyInstaller
- `installer.nsi`: سكريبت NSIS لإنشاء المثبت
- `LICENSE.txt`: ملف الترخيص لتضمينه في المثبت
- `build_installer.py`: سكريبت رئيسي يؤتمت العملية بأكملها
- `create_installer.bat`: ملف دفعي لتسهيل التنفيذ في ويندوز

## خطوات إنشاء المثبت

### الطريقة 1: باستخدام ملف الدفع (موصى بها)

1. ببساطة انقر نقرًا مزدوجًا على `create_installer.bat`
2. اتبع التعليمات التي تظهر على الشاشة
3. سيتم إنشاء المثبت باسم `Ta9affi_v1.04_Setup.exe` في الدليل الحالي

### الطريقة 2: باستخدام بايثون مباشرة

1. افتح موجه الأوامر أو موجه النظام
2. انتقل إلى الدليل الذي توجد فيه الملفات
3. نفذ: `python build_installer.py`
4. سيتم إنشاء المثبت باسم `Ta9affi_v1.04_Setup.exe` في الدليل الحالي

## عملية إنشاء المثبت

تتكون عملية إنشاء المثبت من الخطوات التالية:

1. التحقق من المتطلبات (بايثون، pip، NSIS)
2. تثبيت التبعيات اللازمة
3. إنشاء موارد للمثبت (الصور، الأيقونات)
4. بناء الملف التنفيذي باستخدام PyInstaller
5. بناء المثبت باستخدام NSIS

## حل المشكلات

### خطأ: "بايثون غير مثبت أو غير موجود في متغير PATH"

- تأكد من تثبيت بايثون 3.8 أو أحدث
- تحقق من وجود بايثون في متغير PATH للنظام
- يمكنك التحقق من التثبيت بفتح موجه الأوامر وتنفيذ `python --version`

### خطأ: "NSIS غير مثبت أو غير موجود في متغير PATH"

- تأكد من تثبيت NSIS
- تحقق من وجود NSIS في متغير PATH للنظام
- يمكنك التحقق من التثبيت بفتح موجه الأوامر وتنفيذ `makensis /VERSION`

### خطأ أثناء إنشاء الملف التنفيذي

- تحقق من تثبيت جميع التبعيات بشكل صحيح
- تأكد من وجود جميع الملفات اللازمة في الدليل
- راجع رسائل الخطأ لتحديد المشكلة المحددة

## التخصيص

إذا كنت ترغب في تخصيص المثبت:

- قم بتعديل `installer.nsi` لتغيير إعدادات المثبت
- استبدل `static/img/favicon.ico` بالأيقونة الخاصة بك
- استبدل `static/img/installer-welcome.bmp` بصورة الترحيب الخاصة بك (164x314 بكسل)
- قم بتعديل `LICENSE.txt` حسب احتياجاتك

## ملاحظات إضافية

- المثبت الذي تم إنشاؤه سيعمل على ويندوز 7 والإصدارات الأحدث
- سيتم تثبيت التطبيق في `C:\Program Files\Ta9affi` افتراضيًا
- سيتم إنشاء اختصارات في قائمة ابدأ وعلى سطح المكتب
- يتضمن المثبت برنامج إلغاء تثبيت يمكن تشغيله من لوحة التحكم
