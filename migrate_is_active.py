"""
سكريبت لترحيل قاعدة البيانات وإضافة عمود is_active إلى جدول user
"""

import sqlite3
import os

# مسار قاعدة البيانات
DB_PATH = 'instance/ta9affi_new.db'

def migrate_database():
    """إضافة عمود is_active إلى جدول user"""

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(DB_PATH):
        print(f"قاعدة البيانات {DB_PATH} غير موجودة!")
        return False

    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'is_active' not in column_names:
            print("إضافة عمود is_active إلى جدول user...")
            cursor.execute("ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT 1")
            conn.commit()
            print("تم إضافة العمود بنجاح!")
        else:
            print("العمود is_active موجود بالفعل في جدول user")

        # إغلاق الاتصال
        conn.close()
        return True

    except sqlite3.Error as e:
        print(f"حدث خطأ أثناء ترحيل قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    if migrate_database():
        print("تم ترحيل قاعدة البيانات بنجاح!")
    else:
        print("فشل ترحيل قاعدة البيانات!")
