<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Ta9affi - نظام إدارة البرنامج السنوي للتدريس{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/hover-effects.css') }}">

    {% block styles %}{% endblock %}
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">Ta9affi</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    </li>
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teaching_program') }}">البرنامج السنوي</a>
                        </li>
                        {% if current_user.role == 'teacher' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('manage_schedule') }}">جدول التدريس</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('teacher_notifications') }}">
                                    <i class="fas fa-bell"></i> الإشعارات
                                    {% if unread_notifications_count > 0 %}
                                    <span class="badge bg-danger">{{ unread_notifications_count }}</span>
                                    {% endif %}
                                </a>
                            </li>
                        {% elif current_user.role == 'inspector' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('inspector_notifications') }}">
                                    <i class="fas fa-bell"></i> الإشعارات
                                    {% if unread_notifications_count > 0 %}
                                    <span class="badge bg-danger">{{ unread_notifications_count }}</span>
                                    {% endif %}
                                </a>
                            </li>
                        {% elif current_user.role == 'admin' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('admin_notifications') }}">
                                    <i class="fas fa-bell"></i> الإشعارات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('manage_databases') }}">
                                    <i class="fas fa-database"></i> قواعد البيانات
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('register') }}">إنشاء حساب</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-lg-start mt-5">
        <div class="container p-4">
            <div class="row">
                <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                    <h5 class="text-uppercase">Ta9affi</h5>
                    <p>
                        نظام إدارة البرنامج السنوي للتدريس في التعليم الإبتدائي في الجزائر
                    </p>
                </div>
                <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                    <h5 class="text-uppercase">روابط مفيدة</h5>
                    <ul class="list-unstyled mb-0">
                        <li>
                            <a href="#!" class="text-dark">دليل المستخدم</a>
                        </li>
                        <li>
                            <a href="#!" class="text-dark">اتصل بنا</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="text-center p-3" style="background-color: rgba(0, 0, 0, 0.05);">
            © 2025 Ta9affi - جميع الحقوق محفوظة
            <br>Developed By SPLIN
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
