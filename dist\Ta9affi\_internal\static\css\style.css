/* Custom styles for Ta9affi application */

/* Add some padding to the body for better spacing */
body {
    padding-bottom: 60px;
}

/* Custom card styling */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Dashboard card styling */
.card-body .h3 {
    font-weight: bold;
    margin-bottom: 0;
}

/* Table styling */
.table th {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Form styling */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* Progress bar styling */
.progress {
    height: 0.8rem;
    border-radius: 0.25rem;
}

/* Footer styling */
footer {
    margin-top: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* RTL specific adjustments */
.dropdown-menu-end {
    right: auto;
    left: 0;
}

.me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.me-md-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Custom colors */
.bg-primary {
    background-color: #1976d2 !important;
}

.btn-primary {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-primary:hover {
    background-color: #1565c0;
    border-color: #1565c0;
}

.text-primary {
    color: #1976d2 !important;
}
