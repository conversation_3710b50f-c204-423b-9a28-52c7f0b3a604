from app import app, db
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def delete_primary_database():
    """حذف قاعدة بيانات التعليم الابتدائي الموحدة"""
    with app.app_context():
        # البحث عن مستوى التعليم الابتدائي
        primary_level = EducationalLevel.query.filter_by(name='التعليم الابتدائي').first()
        if primary_level:
            print(f"تم العثور على مستوى: {primary_level.name}")
            
            # البحث عن قاعدة بيانات التعليم الابتدائي
            primary_db = LevelDatabase.query.filter_by(level_id=primary_level.id).first()
            if primary_db:
                print(f"تم العثور على قاعدة البيانات: {primary_db.name}")
                
                # حذف جميع البيانات المرتبطة بقاعدة البيانات
                entries_count = LevelDataEntry.query.filter_by(database_id=primary_db.id).count()
                LevelDataEntry.query.filter_by(database_id=primary_db.id).delete()
                print(f"تم حذف {entries_count} عنصر من قاعدة البيانات")
                
                # حذف قاعدة البيانات
                db.session.delete(primary_db)
                print(f"تم حذف قاعدة البيانات: {primary_db.name}")
                
            # حذف المستوى التعليمي
            db.session.delete(primary_level)
            print(f"تم حذف المستوى التعليمي: {primary_level.name}")
            
            db.session.commit()
            print("تم حذف قاعدة بيانات التعليم الابتدائي بنجاح!")
        else:
            print("لم يتم العثور على قاعدة بيانات التعليم الابتدائي")

if __name__ == "__main__":
    delete_primary_database()
