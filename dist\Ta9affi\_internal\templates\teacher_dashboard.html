{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم الأستاذ</h2>
    </div>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>نسبة الإنجاز</div>
                    <div class="h3">{{ completion_rate|round|int }}%</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#completionRateModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>مكتمل</div>
                    <div class="h3">{{ progress_stats.completed }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#completedModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>قيد التنفيذ</div>
                    <div class="h3">{{ progress_stats.in_progress }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#inProgressModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>مخطط</div>
                    <div class="h3">{{ progress_stats.planned }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#plannedModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt me-1"></i>
                        جدول التدريس الأسبوعي
                    </div>
                    <div class="card-body">
                        <!-- علامات التبويب للأيام -->
                        <ul class="nav nav-tabs" id="weekDaysTabs" role="tablist">
                            {% set days_order = [6, 0, 1, 2, 3, 4, 5] %} <!-- الأحد أولاً -->
                            {% set days = ['الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'] %}

                            {% for day_index in days_order %}
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link {{ 'active' if loop.first else '' }}"
                                            id="day-{{ day_index }}-tab"
                                            data-bs-toggle="tab"
                                            data-bs-target="#day-{{ day_index }}"
                                            type="button"
                                            role="tab"
                                            aria-controls="day-{{ day_index }}"
                                            aria-selected="{{ 'true' if loop.first else 'false' }}">
                                        {{ days[day_index] }}
                                    </button>
                                </li>
                            {% endfor %}
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content pt-3" id="weekDaysTabsContent">
                            {% for day_index in days_order %}
                                <div class="tab-pane fade {{ 'show active' if loop.first else '' }}"
                                     id="day-{{ day_index }}"
                                     role="tabpanel"
                                     aria-labelledby="day-{{ day_index }}-tab">

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>الوقت</th>
                                                    <th>المستوى</th>
                                                    <th>المادة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set day_schedules = schedules|selectattr('day_of_week', 'eq', day_index)|list %}
                                                {% if day_schedules %}
                                                    {% for schedule in day_schedules %}
                                                    <tr>
                                                        <td>
                                                            {% if schedule.start_time and schedule.end_time %}
                                                                {{ schedule.start_time.strftime('%H:%M') }} - {{ schedule.end_time.strftime('%H:%M') }}
                                                            {% else %}
                                                                غير محدد
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ schedule.level.name if schedule.level else 'غير محدد' }}</td>
                                                        <td>{{ schedule.subject.name if schedule.subject else 'غير محدد' }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="3" class="text-center">لا توجد حصص مجدولة لهذا اليوم</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                            <a href="{{ url_for('manage_schedule') }}" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i> تعديل الجدول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-chart-pie me-1"></i>
                        ملخص التقدم
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h6>نسبة الإنجاز الإجمالية</h6>
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ completion_rate|round|int }}%;" aria-valuenow="{{ completion_rate|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ completion_rate|round|int }}%</div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <span class="badge bg-success">مكتمل</span> <span>{{ progress_stats.completed }}</span>
                                    </div>
                                    <div>
                                        <span class="badge bg-warning">قيد التنفيذ</span> <span>{{ progress_stats.in_progress }}</span>
                                    </div>
                                    <div>
                                        <span class="badge bg-danger">مخطط</span> <span>{{ progress_stats.planned }}</span>
                                    </div>
                                    <div>
                                        <span class="badge bg-info">المجموع</span> <span>{{ progress_stats.total }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <h6>التقدم حسب المادة</h6>
                                <div style="height: 200px;">
                                    <canvas id="progressBySubjectChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-bell me-1"></i>
                        آخر الإشعارات
                    </div>
                    <div class="card-body">
                        <div class="notifications-container" style="max-height: 400px; overflow-y: auto;">
                            {% if notifications %}
                                {% for notification in notifications %}
                                <div class="notification-item p-2 mb-2 border-bottom">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1">
                                            {% if notification.is_read %}
                                                {{ notification.title }}
                                            {% else %}
                                                <strong>{{ notification.title }}</strong>
                                                <span class="badge bg-danger ms-2">جديد</span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                    <p class="mb-1">{{ notification.content }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">من: {{ notification.sender.name }}</small>
                                        {% if not notification.is_read %}
                                        <a href="{{ url_for('mark_notification_read', notification_type='inspector_teacher', notification_id=notification.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-check me-1"></i> تم القراءة
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-bell-slash fa-3x mb-3 text-muted"></i>
                                    <p>لا توجد إشعارات جديدة</p>
                                </div>
                            {% endif %}
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ url_for('teacher_notifications') }}" class="btn btn-outline-primary">
                                <i class="fas fa-bell me-1"></i> عرض جميع الإشعارات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-tasks me-1"></i>
                        آخر تحديثات التقدم
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterOptions" aria-expanded="false" aria-controls="filterOptions">
                            <i class="fas fa-filter me-1"></i> خيارات التصفية
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="collapse mb-3" id="filterOptions">
                    <div class="card card-body bg-light">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <label for="filterLevel" class="form-label">
                                    <i class="fas fa-school me-1"></i> المستوى
                                </label>
                                <select class="form-select form-select-sm" id="filterLevel">
                                    <option value="">الكل</option>
                                    {% for level_id, stats in level_stats.items() %}
                                    <option value="{{ level_id }}">{{ stats.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label for="filterSubject" class="form-label">
                                    <i class="fas fa-book me-1"></i> المادة
                                </label>
                                <select class="form-select form-select-sm" id="filterSubject">
                                    <option value="">الكل</option>
                                    {% for subject_id, stats in subject_stats.items() %}
                                    <option value="{{ subject_id }}" data-level="{{ stats.level_id if stats.level_id else '' }}">
                                        {{ stats.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label for="filterStatus" class="form-label">
                                    <i class="fas fa-tasks me-1"></i> الحالة
                                </label>
                                <select class="form-select form-select-sm" id="filterStatus">
                                    <option value="">الكل</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="in_progress">قيد التنفيذ</option>
                                    <option value="planned">مخطط</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label for="sortBy" class="form-label">
                                    <i class="fas fa-sort me-1"></i> ترتيب حسب
                                </label>
                                <select class="form-select form-select-sm" id="sortBy">
                                    <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                                    <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                                    <option value="status">الحالة</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <button class="btn btn-sm btn-primary me-2" id="applyFilters">
                                <i class="fas fa-filter me-1"></i> تطبيق التصفية
                            </button>
                            <button class="btn btn-sm btn-secondary" id="resetFilters">
                                <i class="fas fa-undo me-1"></i> إعادة ضبط
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="progressTable">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" style="width: 100px;">
                                    <i class="fas fa-calendar-day me-1"></i> التاريخ
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-school me-1"></i> المستوى
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-book me-1"></i> المادة
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-sitemap me-1"></i> الميدان
                                </th>
                                <th class="text-center" style="width: 150px;">
                                    <i class="fas fa-bookmark me-1"></i> المادة المعرفية
                                </th>
                                <th class="text-center">
                                    <i class="fas fa-graduation-cap me-1"></i> الكفاءة
                                </th>
                                <th class="text-center" style="width: 100px;">
                                    <i class="fas fa-tasks me-1"></i> الحالة
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-chalkboard me-1"></i> الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if progress_entries %}
                                {% for entry in progress_entries %}
                                <tr data-level="{{ entry.level.id if entry.level else '' }}" data-subject="{{ entry.subject.id if entry.subject else '' }}" data-status="{{ entry.status }}" class="{% if entry.status == 'completed' %}table-success{% elif entry.status == 'in_progress' %}table-warning{% elif entry.status == 'planned' %}table-danger{% endif %}">
                                    <td class="text-center">{{ entry.date.strftime('%Y-%m-%d') if entry.date else '' }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>
                                        {% if entry.competency %}
                                            <div class="competency-text" title="{% if entry.competency.description %}{{ entry.competency.description }}{% elif entry.competency.name %}{{ entry.competency.name }}{% else %}بدون وصف{% endif %}">
                                                {% if entry.competency.description %}
                                                    {{ entry.competency.description[:80] }}{{ '...' if entry.competency.description|length > 80 else '' }}
                                                {% elif entry.competency.name %}
                                                    {{ entry.competency.name }}
                                                {% else %}
                                                    <em>بدون وصف</em>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <em>كفاءة غير معروفة</em>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if entry.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif entry.status == 'in_progress' %}
                                        <span class="badge bg-warning">قيد التنفيذ</span>
                                        {% else %}
                                        <span class="badge bg-danger">مخطط</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}" class="btn btn-sm btn-outline-primary" target="_blank" title="تحضير خطة درس">
                                                <i class="fas fa-chalkboard-teacher"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}" class="btn btn-sm btn-outline-warning" title="تعديل التقدم">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-progress" data-id="{{ entry.id }}" title="حذف التقدم">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد سجلات تقدم حتى الآن</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                <style>
                    .competency-text {
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        cursor: help;
                    }
                </style>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <a href="{{ url_for('teaching_program') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة تقدم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chart-bar me-1"></i>
                نسبة الإنجاز حسب المادة
            </div>
            <div class="card-body">
                <canvas id="progressBySubjectChart" width="100%" height="30"></canvas>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات التقدم
        const progressStats = {
            completed: {{ progress_stats.completed }},
            in_progress: {{ progress_stats.in_progress }},
            planned: {{ progress_stats.planned }},
            total: {{ progress_stats.total }}
        };

        // إحصائيات المستويات
        const levelStats = {
            {% for level_id, stats in level_stats.items() %}
            "{{ level_id }}": {
                name: "{{ stats.name }}",
                completed: {{ stats.completed }},
                in_progress: {{ stats.in_progress }},
                planned: {{ stats.planned }},
                total: {{ stats.total }},
                completion_rate: {{ stats.completion_rate|round|int }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        };

        // إحصائيات المواد
        const subjectStats = {
            {% for subject_id, stats in subject_stats.items() %}
            "{{ subject_id }}": {
                name: "{{ stats.name }}",
                completed: {{ stats.completed }},
                in_progress: {{ stats.in_progress }},
                planned: {{ stats.planned }},
                total: {{ stats.total }},
                completion_rate: {{ stats.completion_rate|round|int }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        };

        // وظائف التصفية والترتيب
        const filterLevel = document.getElementById('filterLevel');
        const filterSubject = document.getElementById('filterSubject');
        const filterStatus = document.getElementById('filterStatus');
        const sortBy = document.getElementById('sortBy');
        const applyFilters = document.getElementById('applyFilters');
        const resetFilters = document.getElementById('resetFilters');
        const progressTable = document.getElementById('progressTable');

        // تحديث قائمة المواد بناءً على المستوى المحدد
        filterLevel.addEventListener('change', function() {
            const selectedLevel = this.value;
            const subjectOptions = filterSubject.querySelectorAll('option');

            // إعادة تعيين قائمة المواد
            filterSubject.value = '';

            // إظهار أو إخفاء المواد بناءً على المستوى
            subjectOptions.forEach(option => {
                if (option.value === '') {
                    // دائمًا إظهار خيار "الكل"
                    option.style.display = '';
                } else {
                    const optionLevel = option.getAttribute('data-level');
                    if (!selectedLevel || selectedLevel === '' || optionLevel === selectedLevel) {
                        option.style.display = '';
                    } else {
                        option.style.display = 'none';
                    }
                }
            });
        });

        // تطبيق التصفية
        function applyFiltering() {
            const levelFilter = filterLevel.value;
            const subjectFilter = filterSubject.value;
            const statusFilter = filterStatus.value;
            const sortOption = sortBy.value;

            // الحصول على جميع الصفوف
            const rows = progressTable.querySelectorAll('tbody tr');

            // تصفية الصفوف
            rows.forEach(row => {
                const rowLevel = row.getAttribute('data-level');
                const rowSubject = row.getAttribute('data-subject');
                const rowStatus = row.getAttribute('data-status');

                let showRow = true;

                // طباعة قيم التصفية للتصحيح
                console.log('Filter values:', { levelFilter, rowLevel, subjectFilter, rowSubject, statusFilter, rowStatus });

                // تطبيق التصفية حسب المستوى
                if (levelFilter && levelFilter !== '' && rowLevel !== levelFilter) {
                    showRow = false;
                }

                // تطبيق التصفية حسب المادة
                if (showRow && subjectFilter && subjectFilter !== '' && rowSubject !== subjectFilter) {
                    showRow = false;
                }

                // تطبيق التصفية حسب الحالة
                if (showRow && statusFilter && statusFilter !== '' && rowStatus !== statusFilter) {
                    showRow = false;
                }

                // إظهار أو إخفاء الصف
                row.style.display = showRow ? '' : 'none';
            });

            // ترتيب الصفوف
            const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');

            if (visibleRows.length > 0) {
                // ترتيب حسب الخيار المحدد
                visibleRows.sort((a, b) => {
                    if (sortOption === 'date_desc') {
                        return new Date(b.cells[0].textContent) - new Date(a.cells[0].textContent);
                    } else if (sortOption === 'date_asc') {
                        return new Date(a.cells[0].textContent) - new Date(b.cells[0].textContent);
                    } else if (sortOption === 'status') {
                        const statusOrder = { 'completed': 1, 'in_progress': 2, 'planned': 3 };
                        const statusA = a.getAttribute('data-status');
                        const statusB = b.getAttribute('data-status');
                        return statusOrder[statusA] - statusOrder[statusB];
                    }
                    return 0;
                });

                // إعادة ترتيب الصفوف في الجدول
                const tbody = progressTable.querySelector('tbody');
                visibleRows.forEach(row => tbody.appendChild(row));

                // إظهار رسالة إذا لم تكن هناك نتائج
                const noResultsRow = progressTable.querySelector('tr[data-no-results]');
                if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            } else {
                // إظهار رسالة إذا لم تكن هناك نتائج
                let noResultsRow = progressTable.querySelector('tr[data-no-results]');
                if (!noResultsRow) {
                    const tbody = progressTable.querySelector('tbody');
                    noResultsRow = document.createElement('tr');
                    noResultsRow.setAttribute('data-no-results', 'true');
                    const cell = document.createElement('td');
                    cell.setAttribute('colspan', '8');
                    cell.classList.add('text-center');
                    cell.textContent = 'لا توجد نتائج مطابقة للتصفية';
                    noResultsRow.appendChild(cell);
                    tbody.appendChild(noResultsRow);
                } else {
                    noResultsRow.style.display = '';
                }
            }
        }

        // إعادة ضبط التصفية
        function resetFiltering() {
            filterLevel.value = '';
            filterSubject.value = '';
            filterStatus.value = '';
            sortBy.value = 'date_desc';

            // إظهار جميع الصفوف
            const rows = progressTable.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (!row.hasAttribute('data-no-results')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // إعادة ترتيب الصفوف حسب التاريخ (الأحدث أولاً)
            const visibleRows = Array.from(rows).filter(row => !row.hasAttribute('data-no-results'));
            visibleRows.sort((a, b) => new Date(b.cells[0].textContent) - new Date(a.cells[0].textContent));

            const tbody = progressTable.querySelector('tbody');
            visibleRows.forEach(row => tbody.appendChild(row));
        }

        // إضافة مستمعي الأحداث
        applyFilters.addEventListener('click', applyFiltering);
        resetFilters.addEventListener('click', resetFiltering);

        // إعداد الرسوم البيانية
        // رسم بياني للتقدم حسب المادة
        const progressCtx = document.getElementById('progressBySubjectChart');
        if (progressCtx) {
            // تحضير البيانات للرسم البياني
            const subjectNames = [];
            const completionRates = [];

            for (const subjectId in subjectStats) {
                if (subjectStats.hasOwnProperty(subjectId)) {
                    subjectNames.push(subjectStats[subjectId].name);
                    completionRates.push(subjectStats[subjectId].completion_rate);
                }
            }

            const progressChart = new Chart(progressCtx, {
            type: 'bar',
            data: {
                labels: subjectNames,
                datasets: [{
                    label: 'نسبة الإنجاز',
                    data: completionRates,
                    backgroundColor: 'rgba(0, 123, 255, 0.8)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        }

        // Manejar la eliminación de entradas de progreso
        const confirmDeleteButton = document.getElementById('confirmDeleteProgress');
        const deleteProgressModal = new bootstrap.Modal(document.getElementById('deleteProgressModal'));

        function setupDeleteButtons() {
            document.querySelectorAll('.delete-progress').forEach(button => {
                button.addEventListener('click', function() {
                    const entryId = this.getAttribute('data-id');
                    confirmDeleteButton.href = `/progress/delete/${entryId}`;
                    deleteProgressModal.show();
                });
            });
        }

        // Configurar los botones de eliminación iniciales
        setupDeleteButtons();

        // Configurar los botones de eliminación en los modales después de que se muestren
        ['completedModal', 'inProgressModal', 'plannedModal'].forEach(modalId => {
            const modal = document.getElementById(modalId);
            modal.addEventListener('shown.bs.modal', function() {
                setupDeleteButtons();
            });
        });
    });
</script>
{% endblock %}

<!-- Modal de confirmación de eliminación -->
<div class="modal fade" id="deleteProgressModal" tabindex="-1" aria-labelledby="deleteProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProgressModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا التقدم؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDeleteProgress" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Completion Rate Details -->
<div class="modal fade" id="completionRateModal" tabindex="-1" aria-labelledby="completionRateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="completionRateModalLabel">تفاصيل نسبة الإنجاز</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>نسبة الإنجاز الإجمالية: {{ completion_rate|round|int }}%</h5>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ completion_rate|round|int }}%;" aria-valuenow="{{ completion_rate|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ completion_rate|round|int }}%</div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>نسبة الإنجاز حسب المستوى</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المستوى</th>
                                        <th>نسبة الإنجاز</th>
                                        <th>مكتمل</th>
                                        <th>قيد التنفيذ</th>
                                        <th>مخطط</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for level_id, stats in level_stats.items() %}
                                    <tr>
                                        <td>{{ stats.name }}</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ stats.completion_rate|round|int }}%;" aria-valuenow="{{ stats.completion_rate|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ stats.completion_rate|round|int }}%</div>
                                            </div>
                                        </td>
                                        <td>{{ stats.completed }}</td>
                                        <td>{{ stats.in_progress }}</td>
                                        <td>{{ stats.planned }}</td>
                                        <td>{{ stats.total }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h5>نسبة الإنجاز حسب المادة</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المادة</th>
                                        <th>نسبة الإنجاز</th>
                                        <th>مكتمل</th>
                                        <th>قيد التنفيذ</th>
                                        <th>مخطط</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subject_id, stats in subject_stats.items() %}
                                    <tr>
                                        <td>{{ stats.name }}</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ stats.completion_rate|round|int }}%;" aria-valuenow="{{ stats.completion_rate|round|int }}" aria-valuemin="0" aria-valuemax="100">{{ stats.completion_rate|round|int }}%</div>
                                            </div>
                                        </td>
                                        <td>{{ stats.completed }}</td>
                                        <td>{{ stats.in_progress }}</td>
                                        <td>{{ stats.planned }}</td>
                                        <td>{{ stats.total }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Completed Tasks -->
<div class="modal fade" id="completedModal" tabindex="-1" aria-labelledby="completedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="completedModalLabel">المهام المكتملة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الميدان</th>
                                <th>المادة المعرفية</th>
                                <th>الكفاءة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set completed_entries = progress_entries|selectattr('status', 'eq', 'completed')|list %}
                            {% if completed_entries %}
                                {% for entry in completed_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.description if entry.competency and entry.competency.description else (entry.competency.name if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}" class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-progress" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام مكتملة</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for In Progress Tasks -->
<div class="modal fade" id="inProgressModal" tabindex="-1" aria-labelledby="inProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="inProgressModalLabel">المهام قيد التنفيذ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الميدان</th>
                                <th>المادة المعرفية</th>
                                <th>الكفاءة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set in_progress_entries = progress_entries|selectattr('status', 'eq', 'in_progress')|list %}
                            {% if in_progress_entries %}
                                {% for entry in in_progress_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.description if entry.competency and entry.competency.description else (entry.competency.name if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}" class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-progress" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام قيد التنفيذ</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Planned Tasks -->
<div class="modal fade" id="plannedModal" tabindex="-1" aria-labelledby="plannedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="plannedModalLabel">المهام المخططة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المستوى</th>
                                <th>المادة</th>
                                <th>الميدان</th>
                                <th>المادة المعرفية</th>
                                <th>الكفاءة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set planned_entries = progress_entries|selectattr('status', 'eq', 'planned')|list %}
                            {% if planned_entries %}
                                {% for entry in planned_entries %}
                                <tr>
                                    <td>{{ entry.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                    <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                    <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                    <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                    <td>{{ entry.competency.description if entry.competency and entry.competency.description else (entry.competency.name if entry.competency else 'غير محدد') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('prepare_lesson', entry_id=entry.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-book"></i>
                                            </a>
                                            <a href="{{ url_for('edit_progress', entry_id=entry.id) }}" class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-progress" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مهام مخططة</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
