{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">
            تعديل قاعدة البيانات
            <a href="{{ url_for('manage_databases') }}" class="btn btn-secondary btn-sm float-end">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </h2>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-edit me-1"></i>
                تعديل قاعدة البيانات: {{ database.name }}
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_database', db_id=database.id) }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ database.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="file_path" class="form-label">مسار الملف</label>
                        <input type="text" class="form-control" id="file_path" name="file_path" value="{{ database.file_path }}" required>
                        <div class="form-text">مثال: data/level1_db.sqlite</div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if database.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">
                            تفعيل قاعدة البيانات
                        </label>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('manage_databases') }}" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
