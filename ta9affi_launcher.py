"""
Ta9affi Launcher - سكريبت لتشغيل تطبيق Ta9affi
الإصدار: 1.04
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
import socket
from flask import Flask

# التحقق مما إذا كان المنفذ 5000 متاحًا
def is_port_available(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) != 0

# فتح المتصفح بعد تأخير قصير
def open_browser():
    time.sleep(2)  # انتظر ثانيتين حتى يبدأ خادم Flask
    webbrowser.open('http://localhost:5000')

# الدالة الرئيسية
def main():
    print("جاري تشغيل Ta9affi v1.04...")

    # التحقق مما إذا كان المنفذ 5000 متاحًا
    if not is_port_available(5000):
        print("خطأ: المنفذ 5000 قيد الاستخدام بالفعل. يرجى إغلاق أي تطبيق يستخدم هذا المنفذ.")
        input("اضغط Enter للخروج...")
        sys.exit(1)

    # الحصول على مسار دليل التطبيق
    if getattr(sys, 'frozen', False):
        # إذا كنا نعمل كملف تنفيذي محزم
        application_path = os.path.dirname(sys.executable)
    else:
        # إذا كنا نعمل كسكريبت
        application_path = os.path.dirname(os.path.abspath(__file__))

    # التغيير إلى دليل التطبيق
    os.chdir(application_path)

    # إنشاء دليل البيانات إذا لم يكن موجودًا
    data_dir = os.path.join(application_path, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # بدء المتصفح في خيط منفصل
    threading.Thread(target=open_browser, daemon=True).start()

    # استيراد وبدء تطبيق Flask
    try:
        from app_new import app, create_sample_data

        # إنشاء بيانات نموذجية إذا لزم الأمر
        with app.app_context():
            create_sample_data()

        # بدء خادم Flask
        app.run(host='0.0.0.0', port=5000, debug=False)
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
