{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <h2 class="mt-4 mb-4">إدارة الإشعارات</h2>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-paper-plane me-1"></i>
                    إرسال إشعار جديد
                </div>
                <div class="card-body">
                    <form action="{{ url_for('send_admin_notification') }}" method="post">
                        <div class="mb-3">
                            <label for="receiver_id" class="form-label">المستلم</label>
                            <select class="form-select" id="receiver_id" name="receiver_id" required>
                                <option value="">اختر المفتش</option>
                                {% for inspector in inspectors %}
                                <option value="{{ inspector.id }}">{{ inspector.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i> إرسال
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-envelope me-1"></i>
                    الإشعارات المرسلة
                </div>
                <div class="card-body">
                    {% if notifications.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المستلم</th>
                                    <th>العنوان</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notification in notifications.items %}
                                <tr>
                                    <td>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ notification.receiver.username }}</td>
                                    <td>
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#notificationModal{{ notification.id }}">
                                            {{ notification.title }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if notification.is_read %}
                                        <span class="badge bg-success">مقروء</span>
                                        {% else %}
                                        <span class="badge bg-warning">غير مقروء</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                
                                <!-- Modal for notification details -->
                                <div class="modal fade" id="notificationModal{{ notification.id }}" tabindex="-1" aria-labelledby="notificationModalLabel{{ notification.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="notificationModalLabel{{ notification.id }}">{{ notification.title }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p><strong>التاريخ:</strong> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                <p><strong>المستلم:</strong> {{ notification.receiver.username }}</p>
                                                <p><strong>الحالة:</strong> 
                                                    {% if notification.is_read %}
                                                    <span class="badge bg-success">مقروء</span>
                                                    {% else %}
                                                    <span class="badge bg-warning">غير مقروء</span>
                                                    {% endif %}
                                                </p>
                                                <hr>
                                                <div class="notification-message">
                                                    {{ notification.message|nl2br }}
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if notifications.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_notifications', page=notifications.prev_num) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in notifications.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                {% if page_num %}
                                    {% if notifications.page == page_num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="{{ url_for('admin_notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin_notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if notifications.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_notifications', page=notifications.next_num) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% else %}
                    <div class="alert alert-info">
                        لا توجد إشعارات مرسلة حتى الآن.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تنسيق النص في الرسائل
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة أي تنسيق إضافي للرسائل هنا
    });
</script>
{% endblock %}
