{% extends 'base.html' %}

{% block content %}
<!-- <PERSON><PERSON><PERSON><PERSON> principal -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-database me-2"></i>
                إدارة قواعد البيانات للمستويات التعليمية
            </h2>
        </div>
    </div>
</div>

<!-- Pestañas de navegación -->
<div class="row mb-4">
    <div class="col-md-12">
        <ul class="nav nav-tabs" id="databaseTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="databases-tab" data-bs-toggle="tab" data-bs-target="#databases" type="button" role="tab" aria-controls="databases" aria-selected="true">
                    <i class="fas fa-database me-1"></i> قواعد البيانات الحالية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab" aria-controls="create" aria-selected="false">
                    <i class="fas fa-plus-circle me-1"></i> إنشاء قاعدة بيانات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-export-tab" data-bs-toggle="tab" data-bs-target="#import-export" type="button" role="tab" aria-controls="import-export" aria-selected="false">
                    <i class="fas fa-exchange-alt me-1"></i> استيراد / تصدير
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="primary-db-tab" data-bs-toggle="tab" data-bs-target="#primary-db" type="button" role="tab" aria-controls="primary-db" aria-selected="false">
                    <i class="fas fa-school me-1"></i> قاعدة بيانات التعليم الابتدائي
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- Contenido de las pestañas -->
<div class="tab-content" id="databaseTabsContent">
    <!-- Pestaña: Bases de datos actuales -->
    <div class="tab-pane fade show active" id="databases" role="tabpanel" aria-labelledby="databases-tab">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-1"></i>
                        قواعد البيانات الحالية
                    </h5>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المستوى التعليمي</th>
                                <th>اسم قاعدة البيانات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db in databases %}
                            <tr>
                                <td>{{ db.level.name }}</td>
                                <td>{{ db.name }}</td>
                                <td>
                                    {% if db.is_active %}
                                    <span class="badge bg-success">مفعلة</span>
                                    {% else %}
                                    <span class="badge bg-danger">معطلة</span>
                                    {% endif %}
                                </td>
                                <td>{{ db.created_at.strftime('%Y-%m-%d') }}</td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_database', db_id=db.id) }}" class="btn btn-sm btn-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_database', db_id=db.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('export_database_data', db_id=db.id) }}" class="btn btn-sm btn-info" title="تصدير إلى Excel">
                                            <i class="fas fa-file-export"></i>
                                        </a>
                                        {% if db.is_active %}
                                        <a href="{{ url_for('toggle_database', db_id=db.id, action='deactivate') }}" class="btn btn-sm btn-secondary" title="تعطيل">
                                            <i class="fas fa-ban"></i>
                                        </a>
                                        {% else %}
                                        <a href="{{ url_for('toggle_database', db_id=db.id, action='activate') }}" class="btn btn-sm btn-success" title="تفعيل">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        {% endif %}
                                        <button class="btn btn-sm btn-danger delete-db" data-id="{{ db.id }}" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not databases %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد قواعد بيانات حالية</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestaña: Crear base de datos -->
    <div class="tab-pane fade" id="create" role="tabpanel" aria-labelledby="create-tab">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-1"></i>
                            إضافة قاعدة بيانات جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="databaseForm" method="POST" action="{{ url_for('add_database') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="level_id" class="form-label">المستوى التعليمي</label>
                                    <select class="form-select" id="level_id" name="level_id" required>
                                        <option value="" selected disabled>اختر المستوى</option>
                                        {% for level in levels %}
                                        <option value="{{ level.id }}">{{ level.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">اسم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="file_path" class="form-label">مسار الملف</label>
                                <input type="text" class="form-control" id="file_path" name="file_path" placeholder="databases/example.db" required>
                                <div class="form-text text-muted">مثال: databases/level1.db</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">مفعلة</label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة قاعدة البيانات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestaña: Importar/Exportar -->
    <div class="tab-pane fade" id="import-export" role="tabpanel" aria-labelledby="import-export-tab">
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-export me-1"></i> تصدير البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>تصدير جميع قواعد البيانات</h6>
                            <p class="text-muted">تصدير جميع قواعد البيانات إلى ملف Excel واحد يحتوي على جميع المستويات والمواد والميادين والمواد المعرفية والكفاءات.</p>
                            <a href="{{ url_for('export_all_databases') }}" class="btn btn-success">
                                <i class="fas fa-file-export me-1"></i> تصدير جميع قواعد البيانات
                            </a>
                        </div>

                        <div class="mb-4">
                            <h6>تصدير المستويات فقط</h6>
                            <p class="text-muted">تصدير المستويات التعليمية فقط إلى ملف Excel.</p>
                            <a href="{{ url_for('export_data', model_name='levels') }}" class="btn btn-info">
                                <i class="fas fa-file-export me-1"></i> تصدير المستويات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-import me-1"></i> استيراد البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>استيراد جميع المستويات</h6>
                            <p class="text-muted">استيراد جميع المستويات والمواد الدراسية والميادين والمواد المعرفية والكفاءات من ملف Excel واحد.</p>
                            <a href="{{ url_for('import_all_levels_page') }}" class="btn btn-danger">
                                <i class="fas fa-file-import me-1"></i> استيراد جميع المستويات
                            </a>
                        </div>

                        <div class="mb-4">
                            <h6>استيراد بيانات لقاعدة بيانات محددة</h6>
                            <p class="text-muted">استيراد بيانات لقاعدة بيانات محددة من ملف Excel.</p>
                            <form id="importForm" method="POST" action="#" enctype="multipart/form-data" onsubmit="submitImportForm(event)">
                                <div class="mb-3">
                                    <label for="import_db_id" class="form-label">قاعدة البيانات</label>
                                    <select class="form-select" id="import_db_id" name="db_id" required>
                                        <option value="" selected disabled>اختر قاعدة البيانات</option>
                                        {% for db in databases %}
                                        {% if db.is_active %}
                                        <option value="{{ db.id }}">{{ db.level.name }} - {{ db.name }}</option>
                                        {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="import_file" class="form-label">ملف البيانات (Excel)</label>
                                    <input type="file" class="form-control" id="import_file" name="file" accept=".xlsx" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-file-import me-1"></i> استيراد البيانات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestaña: Base de datos de educación primaria -->
    <div class="tab-pane fade" id="primary-db" role="tabpanel" aria-labelledby="primary-db-tab">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-school me-1"></i> إنشاء قاعدة بيانات التعليم الابتدائي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير</h6>
                            <p class="mb-0">
                                هذه العملية ستقوم بحذف جميع البيانات الموجودة في قاعدة بيانات التعليم الابتدائي وإعادة إنشائها بالمواد والميادين الثابتة.
                                المواد المعرفية والكفاءات يجب إضافتها يدوياً بعد ذلك.
                            </p>
                        </div>

                        <div class="mb-4">
                            <h6>المواد الدراسية والميادين التي سيتم إنشاؤها:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>اللغة العربية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                فهم المنطوق، التعبير الشفهي، فهم المكتوب، التعبير الكتابي، استكشاف الحرف، كتابة الحرف، إملاء، التراكيب النحوية، الصرف، قراءة إجمالية، قراءة (أداء وفهم)، محفوظات، المشاريع، تطبيقات اللغة، تصحيح التعبير الكتابي، معالجة اللغة العربية، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>الرياضيات</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                الأعداد والحساب، الفضاء والهندسة، المقادير والقياس، تنظيم المعطيات، تطبيقات الرياضيات، معالجة الرياضيات، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التربية الإسلامية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                القرآن والحديث، تهذيب السلوك، مبادئ في العقيدة، مبادئ في السيرة، استعراض النص الشرعي، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التربية العلمية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                الإنسان والصحة، الإنسان والمحيط، المعلمة في الفضاء والزمن، المادة وعلم الأشياء، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التربية المدنية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                الحياة الجماعية، الحياة المدنية، الحياة الديمقراطية والمؤسسات، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التربية الفنية/التشكيلية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                الرسم والتلوين، فن التصميم، النشيد والأغنية التربوية، التذوق الموسيقي والاستماع، القواعد الموسيقية والنظريات، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التاريخ</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                أدوات ومفاهيم مادة التاريخ، التاريخ العام، التاريخ الوطني، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>الجغرافيا</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                أدوات ومفاهيم مادة الجغرافيا، السكان والتنمية، السكان والبيئة، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>التربية البدنية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                الوضعيات والتنقلات، الحركات القاعدية، الهيكلة والبناء، تقويم فصلي
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>الفرنسية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                حصص الفرنسية
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>حفظ القرآن</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                استعراض السورة، استعراض الحديث
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>الأمازيغية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                ⵜⴰⵎⴰⵣⵉⴳⵀ
                                            </small>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <strong>الإنجليزية</strong>
                                        </div>
                                        <div class="card-body">
                                            <small class="text-muted">
                                                English_lesson
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <form method="POST" action="{{ url_for('create_primary_database') }}" onsubmit="return confirm('هل أنت متأكد من إنشاء قاعدة بيانات التعليم الابتدائي؟ سيتم حذف جميع البيانات الموجودة.')">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-database me-1"></i> إنشاء قاعدة بيانات التعليم الابتدائي
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmación de eliminación -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف قاعدة البيانات هذه؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Manejar la eliminación de bases de datos
        const deleteButtons = document.querySelectorAll('.delete-db');
        const confirmDeleteButton = document.getElementById('confirmDelete');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const dbId = this.getAttribute('data-id');
                confirmDeleteButton.href = "{{ url_for('delete_database', db_id=0) }}".replace('0', dbId);

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                deleteModal.show();
            });
        });
    });

    // Función para manejar el envío del formulario de importación
    function submitImportForm(event) {
        event.preventDefault();
        const form = event.target;
        const dbId = document.getElementById('import_db_id').value;

        if (!dbId) {
            alert('الرجاء اختيار قاعدة بيانات');
            return false;
        }

        form.action = "{{ url_for('import_database_data', db_id=0) }}".replace('0', dbId);
        form.submit();
    }
</script>
{% endblock %}
{% endblock %}
