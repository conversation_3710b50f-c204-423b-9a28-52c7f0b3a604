; سكريبت تثبيت لتطبيق Ta9affi v1.04
; تم إنشاؤه لنظام NSIS (Nullsoft Scriptable Install System)

; التعريفات
!define APPNAME "Ta9affi"
!define APPVERSION "1.04"
!define COMPANYNAME "SPLIN"
!define DESCRIPTION "نظام إدارة البرنامج السنوي للتدريس"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 4

; تضمين مكتبات NSIS الحديثة
!include "MUI2.nsh"
!include "FileFunc.nsh"

; الإعدادات العامة
Name "${APPNAME} v${APPVERSION}"
OutFile "Ta9affi_v${APPVERSION}_Setup.exe"
InstallDir "$PROGRAMFILES\${APPNAME}"
InstallDirRegKey HKLM "Software\${APPNAME}" "Install_Dir"
RequestExecutionLevel admin
SetCompressor /SOLID lzma

; المتغيرات
Var StartMenuFolder

; الواجهة
!define MUI_ABORTWARNING
!define MUI_ICON "static\img\favicon.ico"
!define MUI_UNICON "static\img\favicon.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "static\img\installer-welcome.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "static\img\installer-welcome.bmp"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY

; صفحة قائمة ابدأ
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKLM"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "French"
!insertmacro MUI_LANGUAGE "English"

; قسم التثبيت الرئيسي
Section "Ta9affi" SecMain
  SetOutPath "$INSTDIR"

  ; نسخ جميع الملفات من مجلد dist/Ta9affi
  File /r "dist\Ta9affi\*.*"

  ; إنشاء دليل البيانات إذا لم يكن موجودًا
  CreateDirectory "$INSTDIR\data"

  ; تخزين موقع التثبيت
  WriteRegStr HKLM "Software\${APPNAME}" "Install_Dir" "$INSTDIR"

  ; إنشاء مدخلات لإلغاء التثبيت
  WriteUninstaller "$INSTDIR\uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME} v${APPVERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$\"$INSTDIR$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$\"$INSTDIR\Ta9affi.exe$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${APPVERSION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR}

  ; حساب وتخزين حجم التثبيت
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" "$0"

  ; إنشاء اختصارات في قائمة ابدأ
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk" "$INSTDIR\Ta9affi.exe"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" "$INSTDIR\uninstall.exe"
  !insertmacro MUI_STARTMENU_WRITE_END

  ; إنشاء اختصار على سطح المكتب
  CreateShortcut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\Ta9affi.exe"
SectionEnd

; قسم إلغاء التثبيت
Section "Uninstall"
  ; حذف الملفات والمجلدات
  RMDir /r "$INSTDIR\templates"
  RMDir /r "$INSTDIR\static"
  RMDir /r "$INSTDIR\lib"
  RMDir /r "$INSTDIR\_internal"
  Delete "$INSTDIR\*.exe"
  Delete "$INSTDIR\*.dll"
  Delete "$INSTDIR\*.pyd"
  Delete "$INSTDIR\*.manifest"

  ; السؤال إذا كان يجب حذف البيانات
  MessageBox MB_YESNO "هل تريد حذف جميع بيانات التطبيق؟ هذا يشمل قاعدة البيانات وملفات الإعداد." IDNO SkipDataDeletion
    RMDir /r "$INSTDIR\data"
  SkipDataDeletion:

  ; حذف الاختصارات
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"
  Delete "$DESKTOP\${APPNAME}.lnk"

  ; حذف مفاتيح السجل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
  DeleteRegKey HKLM "Software\${APPNAME}"

  ; حذف دليل التثبيت إذا كان فارغًا
  RMDir "$INSTDIR"
SectionEnd
