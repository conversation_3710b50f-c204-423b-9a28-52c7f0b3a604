import pandas as pd
from app import app, db
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def create_test_excel_with_duplicates():
    """إنشاء ملف Excel للاختبار يحتوي على بيانات مكررة مع البيانات الموجودة"""
    
    # بيانات المواد (بعضها مكرر مع الموجود)
    subjects_data = [
        {'name': 'اللغة العربية', 'description': 'مادة اللغة العربية من الملف', 'is_active': True},  # مكرر
        {'name': 'الرياضيات', 'description': 'مادة الرياضيات من الملف', 'is_active': True},  # مكرر
        {'name': 'مادة جديدة للاختبار', 'description': 'مادة جديدة للاختبار', 'is_active': True},  # جديد
    ]
    
    # بيانات الميادين (بعضها مكرر مع الموجود)
    domains_data = [
        {'name': 'فهم المنطوق', 'description': 'ميدان فهم المنطوق من الملف', 'parent_name': 'اللغة العربية', 'is_active': True},  # مكرر
        {'name': 'التعبير الشفهي', 'description': 'ميدان التعبير الشفهي من الملف', 'parent_name': 'اللغة العربية', 'is_active': True},  # مكرر
        {'name': 'ميدان جديد للاختبار', 'description': 'ميدان جديد للاختبار', 'parent_name': 'مادة جديدة للاختبار', 'is_active': True},  # جديد
        {'name': 'الأعداد والحساب', 'description': 'ميدان الأعداد والحساب من الملف', 'parent_name': 'الرياضيات', 'is_active': True},  # مكرر
    ]
    
    # بيانات المواد المعرفية
    materials_data = [
        {'name': 'نص منطوق جديد', 'description': 'مادة معرفية جديدة', 'parent_name': 'فهم المنطوق', 'is_active': True},
        {'name': 'حوار جديد', 'description': 'مادة معرفية جديدة', 'parent_name': 'التعبير الشفهي', 'is_active': True},
        {'name': 'مادة معرفية للاختبار', 'description': 'مادة معرفية للاختبار', 'parent_name': 'ميدان جديد للاختبار', 'is_active': True},
    ]
    
    # إنشاء ملف Excel
    with pd.ExcelWriter('test_import_with_clear.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)
    
    print("تم إنشاء ملف test_import_with_clear.xlsx للاختبار")

def test_import_with_clear():
    """اختبار استيراد البيانات مع حذف البيانات الحالية"""
    with app.app_context():
        # البحث عن قاعدة بيانات السنة الأولى ابتدائي
        level = EducationalLevel.query.filter_by(name='السنة الأولى ابتدائي').first()
        if not level:
            print("لا توجد قاعدة بيانات للسنة الأولى ابتدائي. يرجى إنشاؤها أولاً.")
            return
            
        level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
        if not level_db:
            print("لا توجد قاعدة بيانات للسنة الأولى ابتدائي. يرجى إنشاؤها أولاً.")
            return
        
        print(f"قاعدة البيانات: {level_db.name}")
        
        # عد العناصر قبل الاستيراد
        subjects_before = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
        domains_before = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
        materials_before = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='material').count()
        
        print(f"قبل الاستيراد: {subjects_before} مادة، {domains_before} ميدان، {materials_before} مادة معرفية")
        
        # محاكاة حذف البيانات الحالية (الوضع الافتراضي الجديد)
        print("حذف جميع البيانات الحالية...")
        deleted_count = LevelDataEntry.query.filter_by(database_id=level_db.id).count()
        LevelDataEntry.query.filter_by(database_id=level_db.id).delete()
        print(f"تم حذف {deleted_count} عنصر")
        
        # قراءة ملف Excel
        df = pd.read_excel('test_import_with_clear.xlsx', sheet_name=None)
        
        # معالجة كل ورقة
        for sheet_name, sheet_data in df.items():
            if sheet_name.lower() in ['subjects', 'domains', 'materials']:
                entry_type = sheet_name.lower().rstrip('s')  # إزالة s في النهاية
                
                added_count = 0
                
                for _, row in sheet_data.iterrows():
                    row_dict = row.to_dict()
                    
                    name = row_dict.get('name', '').strip()
                    if not name:  # تجاهل الصفوف الفارغة
                        continue
                    
                    # الحصول على معرف العنصر الأب إذا كان مطلوباً
                    parent_id = None
                    if 'parent_name' in row_dict and entry_type != 'subject':
                        parent_name = row_dict['parent_name'].strip()
                        if parent_name:
                            parent_type = {
                                'domain': 'subject',
                                'material': 'domain',
                                'competency': 'material'
                            }[entry_type]
                            
                            parent = LevelDataEntry.query.filter_by(
                                database_id=level_db.id,
                                entry_type=parent_type,
                                name=parent_name
                            ).first()
                            
                            if parent:
                                parent_id = parent.id
                    
                    # إنشاء عنصر جديد
                    new_entry = LevelDataEntry(
                        database_id=level_db.id,
                        entry_type=entry_type,
                        parent_id=parent_id,
                        name=name,
                        description=row_dict.get('description', '').strip(),
                        is_active=row_dict.get('is_active', True),
                        order_num=row_dict.get('order_num', 0)
                    )
                    
                    db.session.add(new_entry)
                    added_count += 1
                    print(f"تم إضافة: {name} ({entry_type})")
                
                print(f"ورقة {sheet_name}: تم إضافة {added_count} عنصر")
        
        db.session.commit()
        
        # عد العناصر بعد الاستيراد
        subjects_after = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
        domains_after = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()
        materials_after = LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='material').count()
        
        print(f"بعد الاستيراد: {subjects_after} مادة، {domains_after} ميدان، {materials_after} مادة معرفية")
        print(f"✅ النتيجة: قاعدة بيانات نظيفة بدون تكرار!")

if __name__ == "__main__":
    create_test_excel_with_duplicates()
    test_import_with_clear()
