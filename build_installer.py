"""
سكريبت رئيسي لإنشاء مثبت Ta9affi v1.04
"""

import os
import sys
import subprocess
import shutil
import time

def check_requirements():
    """التحقق من توفر جميع المتطلبات"""
    print("جاري التحقق من المتطلبات...")

    # التحقق من بايثون
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("خطأ: يجب توفر بايثون 3.8 أو أحدث")
        return False

    # التحقق من pip
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("خطأ: pip غير مثبت أو لا يعمل بشكل صحيح")
        return False

    # التحقق من NSIS
    try:
        result = subprocess.run(["makensis", "/VERSION"], check=False, capture_output=True)
        if result.returncode != 0:
            print("تحذير: NSIS غير مثبت أو غير موجود في متغير PATH")
            print("سنقوم بإنشاء الملف التنفيذي فقط دون إنشاء المثبت")
            # لا نعيد false هنا لنسمح بالمتابعة
        else:
            print("NSIS مثبت بنجاح")
    except FileNotFoundError:
        print("تحذير: NSIS غير مثبت أو غير موجود في متغير PATH")
        print("سنقوم بإنشاء الملف التنفيذي فقط دون إنشاء المثبت")
        # لا نعيد false هنا لنسمح بالمتابعة

    return True

def install_dependencies():
    """تثبيت التبعيات اللازمة"""
    print("جاري تثبيت التبعيات...")

    dependencies = [
        "flask",
        "flask-sqlalchemy",
        "flask-login",
        "flask-wtf",
        "werkzeug",
        "pandas",
        "openpyxl",
        "pyinstaller"
    ]

    for dep in dependencies:
        print(f"جاري تثبيت {dep}...")
        subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)

    print("تم تثبيت جميع التبعيات بنجاح")

def create_installer_resources():
    """إنشاء الموارد اللازمة للمثبت"""
    print("جاري إنشاء موارد المثبت...")

    # إنشاء دليل لصور المثبت إذا لم يكن موجودًا
    os.makedirs("static/img", exist_ok=True)

    # التحقق من وجود أيقونة التطبيق
    if not os.path.exists("static/img/favicon.ico"):
        print("تحذير: لم يتم العثور على favicon.ico في static/img/")
        print("سيتم استخدام أيقونة افتراضية")

    # التحقق من وجود صورة الترحيب للمثبت
    if not os.path.exists("static/img/installer-welcome.bmp"):
        print("جاري إنشاء صورة الترحيب للمثبت...")
        # إنشاء صورة ترحيب بسيطة (164x314 بكسل)
        try:
            from PIL import Image, ImageDraw, ImageFont
            img = Image.new('RGB', (164, 314), color=(0, 120, 215))
            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("arial.ttf", 16)
                draw.text((20, 150), "Ta9affi v1.04", fill=(255, 255, 255), font=font)
            except:
                draw.text((20, 150), "Ta9affi v1.04", fill=(255, 255, 255))
            img.save("static/img/installer-welcome.bmp")
        except ImportError:
            print("تحذير: لم يتمكن من إنشاء صورة الترحيب")
            print("قم بتثبيت Pillow باستخدام: pip install pillow")

def build_executable():
    """بناء الملف التنفيذي باستخدام PyInstaller"""
    print("جاري بناء الملف التنفيذي...")

    # تشغيل سكريبت بناء الملف التنفيذي
    subprocess.run([sys.executable, "build_executable.py"], check=True)

def build_installer():
    """بناء المثبت باستخدام NSIS"""
    print("جاري بناء المثبت...")

    # تشغيل NSIS لإنشاء المثبت
    subprocess.run(["makensis", "installer.nsi"], check=True)

    # التحقق من إنشاء المثبت
    if os.path.exists("Ta9affi_v1.04_Setup.exe"):
        print("تم إنشاء المثبت بنجاح: Ta9affi_v1.04_Setup.exe")
    else:
        print("خطأ: لم يتمكن من إنشاء المثبت")

def main():
    print("=== إنشاء مثبت Ta9affi v1.04 ===")

    # التحقق من المتطلبات
    if not check_requirements():
        print("يرجى تثبيت المتطلبات الناقصة والمحاولة مرة أخرى")
        return

    # تثبيت التبعيات
    install_dependencies()

    # إنشاء موارد المثبت
    create_installer_resources()

    # بناء الملف التنفيذي
    build_executable()

    # التحقق من توفر NSIS قبل بناء المثبت
    try:
        result = subprocess.run(["makensis", "/VERSION"], check=False, capture_output=True)
        if result.returncode == 0:
            # بناء المثبت فقط إذا كان NSIS متوفرًا
            build_installer()
        else:
            print("تم تخطي بناء المثبت لأن NSIS غير متوفر")
    except FileNotFoundError:
        print("تم تخطي بناء المثبت لأن NSIS غير متوفر")

    print("تم اكتمال العملية")

if __name__ == "__main__":
    main()
