/* مؤثرات التحويم على البطاقات الملونة */
.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger, .card.bg-info {
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card.bg-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(13, 110, 253, 0.3);
}

.card.bg-success:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(25, 135, 84, 0.3);
}

.card.bg-warning:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(255, 193, 7, 0.3);
}

.card.bg-danger:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(220, 53, 69, 0.3);
}

.card.bg-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(13, 202, 240, 0.3);
}

/* مؤثرات التحويم على البطاقات العادية في الصفحة الرئيسية */
.home-card {
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
}

.home-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 20px rgba(25, 118, 210, 0.2);
    border-color: rgba(25, 118, 210, 0.3);
}

.home-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, rgba(25, 118, 210, 0.1), transparent);
    transition: height 0.5s ease;
}

.home-card:hover:before {
    height: 100%;
}

/* مؤثر النبض على الأرقام */
.card:hover .h3 {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* مؤثرات إضافية للبطاقات */
.card.bg-primary:hover .card-footer,
.card.bg-success:hover .card-footer,
.card.bg-warning:hover .card-footer,
.card.bg-danger:hover .card-footer,
.card.bg-info:hover .card-footer {
    background-color: rgba(255, 255, 255, 0.1);
}

.card:hover .fa-angle-left {
    animation: bounce-left 1s infinite;
}

@keyframes bounce-left {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-5px);
    }
}

/* مؤثرات الأيقونات في الصفحة الرئيسية */
.home-icon {
    transition: all 0.4s ease;
    color: #1976d2;
}

.home-card:hover .home-icon {
    animation: icon-bounce 1s ease infinite;
    color: #1565c0;
}

@keyframes icon-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* مؤثرات العناوين في البطاقات */
.card-title {
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.home-card:hover .card-title {
    transform: scale(1.05);
    color: #1976d2;
}

.card-title:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background-color: #1976d2;
    transition: all 0.3s ease;
}

.home-card:hover .card-title:after {
    width: 100%;
    left: 0;
}

/* مؤثرات الأزرار */
.btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-hover-effect:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-hover-effect:hover:after {
    height: 100%;
}

.btn-hover-effect:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.18);
}

/* مؤثرات البطاقة الرئيسية */
.main-card {
    transition: all 0.5s ease;
    position: relative;
    overflow: hidden;
}

.main-card:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.main-card:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease;
}

.main-card:hover:after {
    opacity: 1;
    transform: scale(1);
}

/* مؤثرات الصورة الرئيسية */
.main-image {
    transition: all 0.5s ease;
    transform-origin: center center;
}

.main-card:hover .main-image {
    transform: scale(1.05);
    filter: drop-shadow(0 0 10px rgba(25, 118, 210, 0.3));
}
