@echo off
echo ===== جاري إنشاء مثبت Ta9affi v1.04 =====
echo.

REM التحقق من تثبيت بايثون
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: بايثون غير مثبت أو غير موجود في متغير PATH
    echo يرجى تثبيت بايثون 3.8 أو أحدث من https://www.python.org/downloads/
    pause
    exit /b 1
)

REM التحقق من تثبيت NSIS
makensis /VERSION >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: NSIS غير مثبت أو غير موجود في متغير PATH
    echo يرجى تثبيت NSIS من https://nsis.sourceforge.io/Download
    pause
    exit /b 1
)

REM تشغيل سكريبت إنشاء المثبت
python build_installer.py

echo.
if %ERRORLEVEL% EQ 0 (
    echo ===== تم اكتمال العملية بنجاح =====
) else (
    echo ===== حدث خطأ في العملية =====
)
pause
