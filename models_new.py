"""
نماذج قاعدة البيانات الجديدة
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime

# تهيئة قاعدة البيانات
db = SQLAlchemy()

# أدوار المستخدمين
class Role:
    ADMIN = 'admin'
    INSPECTOR = 'inspector'
    TEACHER = 'teacher'

# نموذج المستخدم
class User(db.Model, UserMixin):
    """نموذج المستخدم مع دعم تسجيل الدخول"""

    @property
    def is_authenticated(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False)
    _is_active = db.Column('is_active', db.Boolean, default=True)  # لتفعيل أو تعطيل الحساب
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    schedules = db.relationship('Schedule', backref='user', lazy=True)
    progress_entries = db.relationship('ProgressEntry', backref='user', lazy=True)

    # للمفتشين: الأساتذة الذين يشرفون عليهم
    supervised_teachers = db.relationship('User',
                                         secondary='inspector_teacher',
                                         primaryjoin="and_(User.id==inspector_teacher.c.inspector_id, User.role=='inspector')",
                                         secondaryjoin="and_(User.id==inspector_teacher.c.teacher_id, User.role=='teacher')",
                                         backref=db.backref('inspectors', lazy='dynamic'),
                                         lazy='dynamic')

# علاقة المفتش-الأستاذ
inspector_teacher = db.Table('inspector_teacher',
    db.Column('inspector_id', db.Integer, db.ForeignKey('user.id'), primary_key=True),
    db.Column('teacher_id', db.Integer, db.ForeignKey('user.id'), primary_key=True)
)

# نموذج المستوى التعليمي
class EducationalLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)  # لتفعيل أو تعطيل المستوى
    database_prefix = db.Column(db.String(20), nullable=True)  # بادئة لقاعدة البيانات الخاصة بالمستوى

    # العلاقات
    subjects = db.relationship('Subject', backref='educational_level', lazy=True)
    databases = db.relationship('LevelDatabase', backref='level', lazy=True, cascade='all, delete-orphan')

# نموذج المادة الدراسية
class Subject(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    domains = db.relationship('Domain', backref='subject', lazy=True)

# نموذج الميدان
class Domain(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)

    # العلاقات
    knowledge_materials = db.relationship('KnowledgeMaterial', backref='domain', lazy=True)

# نموذج المادة المعرفية
class KnowledgeMaterial(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    domain_id = db.Column(db.Integer, db.ForeignKey('domain.id'), nullable=False)

    # العلاقات
    competencies = db.relationship('Competency', backref='knowledge_material', lazy=True)

# نموذج الكفاءة المستهدفة
class Competency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.Text, nullable=False)
    knowledge_material_id = db.Column(db.Integer, db.ForeignKey('knowledge_material.id'), nullable=False)

    # العلاقات
    progress_entries = db.relationship('ProgressEntry', backref='competency', lazy=True)

# نموذج جدول الأستاذ
class Schedule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0=الاثنين، 6=الأحد
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    subject = db.relationship('Subject')
    level = db.relationship('EducationalLevel')

# نموذج تسجيل التقدم
class ProgressEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    competency_id = db.Column(db.Integer, db.ForeignKey('competency.id'), nullable=False)

    # إضافة العلاقات مع المستوى والمادة والميدان والمادة المعرفية
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    domain_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    material_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))

    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)  # 'completed', 'in_progress', 'planned'
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    level = db.relationship('EducationalLevel', foreign_keys=[level_id], backref='progress_entries')
    subject = db.relationship('LevelDataEntry', foreign_keys=[subject_id], backref='subject_progress_entries')
    domain = db.relationship('LevelDataEntry', foreign_keys=[domain_id], backref='domain_progress_entries')
    material = db.relationship('LevelDataEntry', foreign_keys=[material_id], backref='material_progress_entries')

# نموذج قاعدة البيانات الخاصة بكل مستوى
class LevelDatabase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)  # اسم قاعدة البيانات
    file_path = db.Column(db.String(255), nullable=False)  # مسار ملف قاعدة البيانات
    is_active = db.Column(db.Boolean, default=True)  # حالة قاعدة البيانات
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    data_entries = db.relationship('LevelDataEntry', backref='database', lazy=True, cascade='all, delete-orphan')

# نموذج بيانات كل مستوى
class LevelDataEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    database_id = db.Column(db.Integer, db.ForeignKey('level_database.id'), nullable=False)
    entry_type = db.Column(db.String(50), nullable=False)  # نوع البيانات (مادة، ميدان، مادة معرفية، كفاءة)
    parent_id = db.Column(db.Integer, nullable=True)  # معرف العنصر الأب (للتسلسل الهرمي)
    name = db.Column(db.String(255), nullable=False)  # اسم العنصر
    description = db.Column(db.Text, nullable=True)  # وصف العنصر
    order_num = db.Column(db.Integer, default=0)  # ترتيب العنصر
    is_active = db.Column(db.Boolean, default=True)  # حالة العنصر
    extra_data = db.Column(db.Text, nullable=True)  # بيانات إضافية (JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# نموذج الإشعارات بين الإدارة والمفتشين
class AdminInspectorNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_admin_inspector_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_admin_inspector_notifications')


# نموذج الإشعارات بين المفتشين والأساتذة
class InspectorTeacherNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_inspector_teacher_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_inspector_teacher_notifications')
