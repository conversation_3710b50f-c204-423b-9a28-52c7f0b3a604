import pandas as pd
from app import app, db
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def create_test_excel():
    """إنشاء ملف Excel للاختبار يحتوي على بيانات مكررة"""
    
    # بيانات المواد (بعضها مكرر)
    subjects_data = [
        {'name': 'اللغة العربية', 'description': 'مادة اللغة العربية', 'is_active': True},
        {'name': 'الرياضيات', 'description': 'مادة الرياضيات', 'is_active': True},
        {'name': 'اللغة العربية', 'description': 'مادة اللغة العربية مكررة', 'is_active': True},  # مكرر
        {'name': 'التربية الإسلامية', 'description': 'مادة التربية الإسلامية', 'is_active': True},
    ]
    
    # بيانات الميادين (بعضها مكرر)
    domains_data = [
        {'name': 'فهم المنطوق', 'description': 'ميدان فهم المنطوق', 'parent_name': 'اللغة العربية', 'is_active': True},
        {'name': 'التعبير الشفهي', 'description': 'ميدان التعبير الشفهي', 'parent_name': 'اللغة العربية', 'is_active': True},
        {'name': 'فهم المنطوق', 'description': 'ميدان فهم المنطوق مكرر', 'parent_name': 'اللغة العربية', 'is_active': True},  # مكرر
        {'name': 'الأعداد والحساب', 'description': 'ميدان الأعداد والحساب', 'parent_name': 'الرياضيات', 'is_active': True},
        {'name': 'القرآن والحديث', 'description': 'ميدان القرآن والحديث', 'parent_name': 'التربية الإسلامية', 'is_active': True},
    ]
    
    # بيانات المواد المعرفية (بعضها مكرر)
    materials_data = [
        {'name': 'نص منطوق', 'description': 'مادة معرفية نص منطوق', 'parent_name': 'فهم المنطوق', 'is_active': True},
        {'name': 'حوار', 'description': 'مادة معرفية حوار', 'parent_name': 'التعبير الشفهي', 'is_active': True},
        {'name': 'نص منطوق', 'description': 'مادة معرفية نص منطوق مكرر', 'parent_name': 'فهم المنطوق', 'is_active': True},  # مكرر
        {'name': 'الأعداد الطبيعية', 'description': 'مادة معرفية الأعداد الطبيعية', 'parent_name': 'الأعداد والحساب', 'is_active': True},
    ]
    
    # إنشاء ملف Excel
    with pd.ExcelWriter('test_duplicate_data.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)
    
    print("تم إنشاء ملف test_duplicate_data.xlsx للاختبار")

def test_import_with_duplicates():
    """اختبار استيراد البيانات مع تجنب التكرار"""
    with app.app_context():
        # البحث عن قاعدة بيانات التعليم الابتدائي
        primary_level = EducationalLevel.query.filter_by(name='التعليم الابتدائي').first()
        if not primary_level:
            print("لا توجد قاعدة بيانات للتعليم الابتدائي. يرجى إنشاؤها أولاً.")
            return
            
        primary_db = LevelDatabase.query.filter_by(level_id=primary_level.id).first()
        if not primary_db:
            print("لا توجد قاعدة بيانات للتعليم الابتدائي. يرجى إنشاؤها أولاً.")
            return
        
        print(f"قاعدة البيانات: {primary_db.name}")
        
        # عد العناصر قبل الاستيراد
        subjects_before = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='subject').count()
        domains_before = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='domain').count()
        materials_before = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='material').count()
        
        print(f"قبل الاستيراد: {subjects_before} مادة، {domains_before} ميدان، {materials_before} مادة معرفية")
        
        # قراءة ملف Excel
        df = pd.read_excel('test_duplicate_data.xlsx', sheet_name=None)
        
        # معالجة كل ورقة
        for sheet_name, sheet_data in df.items():
            if sheet_name.lower() in ['subjects', 'domains', 'materials']:
                entry_type = sheet_name.lower().rstrip('s')  # إزالة s في النهاية
                
                added_count = 0
                skipped_count = 0
                
                for _, row in sheet_data.iterrows():
                    row_dict = row.to_dict()
                    
                    name = row_dict.get('name', '').strip()
                    if not name:  # تجاهل الصفوف الفارغة
                        continue
                    
                    # التحقق من عدم وجود العنصر مسبقاً لتجنب التكرار
                    existing_entry = LevelDataEntry.query.filter_by(
                        database_id=primary_db.id,
                        entry_type=entry_type,
                        name=name
                    ).first()
                    
                    if existing_entry:
                        skipped_count += 1
                        print(f"تم تجاهل العنصر المكرر: {name} ({entry_type})")
                        continue  # تجاهل العنصر إذا كان موجوداً مسبقاً
                    
                    # الحصول على معرف العنصر الأب إذا كان مطلوباً
                    parent_id = None
                    if 'parent_name' in row_dict and entry_type != 'subject':
                        parent_name = row_dict['parent_name'].strip()
                        if parent_name:
                            parent_type = {
                                'domain': 'subject',
                                'material': 'domain',
                                'competency': 'material'
                            }[entry_type]
                            
                            parent = LevelDataEntry.query.filter_by(
                                database_id=primary_db.id,
                                entry_type=parent_type,
                                name=parent_name
                            ).first()
                            
                            if parent:
                                parent_id = parent.id
                    
                    # إنشاء عنصر جديد
                    new_entry = LevelDataEntry(
                        database_id=primary_db.id,
                        entry_type=entry_type,
                        parent_id=parent_id,
                        name=name,
                        description=row_dict.get('description', '').strip(),
                        is_active=row_dict.get('is_active', True),
                        order_num=row_dict.get('order_num', 0)
                    )
                    
                    db.session.add(new_entry)
                    added_count += 1
                    print(f"تم إضافة: {name} ({entry_type})")
                
                print(f"ورقة {sheet_name}: تم إضافة {added_count} عنصر، تم تجاهل {skipped_count} عنصر مكرر")
        
        db.session.commit()
        
        # عد العناصر بعد الاستيراد
        subjects_after = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='subject').count()
        domains_after = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='domain').count()
        materials_after = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='material').count()
        
        print(f"بعد الاستيراد: {subjects_after} مادة، {domains_after} ميدان، {materials_after} مادة معرفية")
        print(f"تم إضافة: {subjects_after - subjects_before} مادة، {domains_after - domains_before} ميدان، {materials_after - materials_before} مادة معرفية")

if __name__ == "__main__":
    create_test_excel()
    test_import_with_duplicates()
