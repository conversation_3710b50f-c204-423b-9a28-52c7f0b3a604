from app import app, db
from models_new import EducationalLevel, LevelDatabase, LevelDataEntry

def test_create_primary_database():
    with app.app_context():
        # البحث عن مستوى التعليم الابتدائي
        primary_level = EducationalLevel.query.filter_by(name='التعليم الابتدائي').first()
        if not primary_level:
            # إنشاء مستوى التعليم الابتدائي إذا لم يكن موجوداً
            primary_level = EducationalLevel(
                name='التعليم الابتدائي',
                is_active=True,
                database_prefix='primary'
            )
            db.session.add(primary_level)
            db.session.flush()

        # البحث عن قاعدة بيانات التعليم الابتدائي
        primary_db = LevelDatabase.query.filter_by(level_id=primary_level.id).first()
        if not primary_db:
            # إنشاء قاعدة بيانات جديدة
            primary_db = LevelDatabase(
                level_id=primary_level.id,
                name='قاعدة بيانات التعليم الابتدائي',
                file_path='data/primary_education.db',
                is_active=True
            )
            db.session.add(primary_db)
            db.session.flush()
        else:
            # حذف جميع البيانات الموجودة في قاعدة البيانات
            LevelDataEntry.query.filter_by(database_id=primary_db.id).delete()

        # تعريف المواد والميادين
        subjects_domains = {
            'اللغة العربية': [
                'فهم المنطوق',
                'التعبير الشفهي',
                'فهم المكتوب (ألعاب قرائية)',
                'التعبير الشفهي ( صيغ )',
                'التعبير الشفهي ( إنتاج)',
                'التعبير الكتابي (أركب)',
                'التعبير الكتابي (إنتاج كتابي)',
                'استكشاف الحرف',
                'كتابة الحرف',
                'إملاء',
                'التراكيب النحوية',
                'الصرف',
                'قراءة اجمالية',
                'قراءة (اداء و فهم)',
                'محفوظات',
                'المشاريع',
                'تطبيقات اللغة',
                'تصحيح التعبير الكتابي',
                'معالجة اللغة العربية',
                'تقويم فصلي في اللغة العربية'
            ],
            'الرياضيات': [
                'الاعداد و الحساب',
                'الفضاء و الهندسة',
                'المقادير و القياس',
                'تنظيم المعطيات',
                'تطبيقات الرياضيات',
                'معالجة الرياضيات',
                'تقويم فصلي في الرياضيات'
            ],
            'التربية الإسلامية': [
                'القرآن والحديث',
                'تهذيب السلوك',
                'مبادئ في العقيدة',
                'مبادئ في السيرة',
                'استعراض النص الشرعي',
                'تقويم فصلي في التربية الإسلامية'
            ],
            'الفرنسية': [
                'حصص الفرنسية'
            ],
            'التربية العلمية': [
                'الإنسان و الصحة',
                'الإنسان و المحيط',
                'المعلمة في الفضاء و الزمن',
                'المادة و علم الأشياء',
                'تقويم فصلي في التربية العلمية'
            ],
            'التربية المدنية': [
                'الحياة الجماعية',
                'الحياة المدنية',
                'الحياة الديمقراطية و المؤسسات',
                'تقويم فصلي في التربية المدنية'
            ],
            'التربية الفنية /التشكيلية': [
                'الرسم و التلوين',
                'فن التصميم',
                'النشيد و الأغنية التربوية',
                'التذوق الموسيقي و الاستماع',
                'القواعد الموسيقية و النظريات',
                'تقويم فصلي التربية الفنية'
            ],
            'التاريخ': [
                'أدوات و مفاهيم مادة التاريخ',
                'التاريخ العام',
                'التاريخ الوطني',
                'تقويم فصلي في التاريخ و الغرافيا'
            ],
            'الجغرافيا': [
                'أدوات و مفاهيم مادة الجغرافيا',
                'السكان و التنمية',
                'السكان و البيئة',
                'تقويم فصلي  في التاريخ و الجغرافيا'
            ],
            'التربية البدنية': [
                'الوضعيات و التنقلات',
                'الحركات القاعدية',
                'الهيكلة و البناء',
                'تقويم فصلي في التربية البدنية'
            ],
            'حفظ القرآن': [
                'استعراض السورة',
                'استعراض الحديث'
            ],
            'الأمازيغية': [
                'ⵜⴰⵎⴰⵣⵉⴳⵀ'
            ],
            'الإنجليزية': [
                'English_lesson'
            ]
        }

        # إضافة المواد والميادين
        for subject_name, domains in subjects_domains.items():
            # إضافة المادة
            subject_entry = LevelDataEntry(
                database_id=primary_db.id,
                entry_type='subject',
                name=subject_name,
                description=f'مادة {subject_name}',
                is_active=True,
                order_num=list(subjects_domains.keys()).index(subject_name) + 1
            )
            db.session.add(subject_entry)
            db.session.flush()  # للحصول على معرف المادة

            # إضافة الميادين للمادة
            for i, domain_name in enumerate(domains):
                domain_entry = LevelDataEntry(
                    database_id=primary_db.id,
                    entry_type='domain',
                    parent_id=subject_entry.id,
                    name=domain_name,
                    description=f'ميدان {domain_name} في مادة {subject_name}',
                    is_active=True,
                    order_num=i + 1
                )
                db.session.add(domain_entry)

        db.session.commit()
        print('تم إنشاء قاعدة بيانات التعليم الابتدائي بنجاح!')
        
        # عرض النتائج
        subjects = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='subject').all()
        domains = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='domain').all()
        
        print(f'تم إنشاء {len(subjects)} مادة دراسية')
        print(f'تم إنشاء {len(domains)} ميدان')
        
        for subject in subjects:
            subject_domains = LevelDataEntry.query.filter_by(database_id=primary_db.id, entry_type='domain', parent_id=subject.id).all()
            print(f'- {subject.name}: {len(subject_domains)} ميدان')

if __name__ == "__main__":
    test_create_primary_database()
